
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import BarcodeScanner from '../BarcodeScanner';

declare global {
  var zxingMocks: {
    mockReset: ReturnType<typeof vi.fn>;
    mockDecodeFromVideoDevice: ReturnType<typeof vi.fn>;
    mockDecodeFromVideoElement: ReturnType<typeof vi.fn>;
    mockListVideoInputDevices: ReturnType<typeof vi.fn>;
  };
}

// Mock the logging service
vi.mock('@services/loggingService', () => ({
  createLogger: vi.fn(() => ({
    info: vi.fn().mockResolvedValue(undefined),
    debug: vi.fn().mockResolvedValue(undefined),
    warn: vi.fn().mockResolvedValue(undefined),
    error: vi.fn().mockResolvedValue(undefined),
    logError: vi.fn().mockResolvedValue(undefined),
    logUserAction: vi.fn().mockResolvedValue(undefined),
  })),
}));

global.zxingMocks = global.zxingMocks || {};

vi.mock('@zxing/browser', () => {
  const mockReset = vi.fn();
  const mockDecodeFromVideoDevice = vi.fn();
  const mockDecodeFromVideoElement = vi.fn();
  const mockListVideoInputDevices = vi.fn().mockResolvedValue([
    { deviceId: 'camera1', label: 'Camera 1', kind: 'videoinput' }
  ]);

  const BrowserMultiFormatReaderMock: any = vi.fn().mockImplementation(() => ({
    decodeFromVideoDevice: mockDecodeFromVideoDevice,
    decodeFromVideoElement: mockDecodeFromVideoElement,
    reset: mockReset,
    hints: new Map(),
  }));

  // Add static method
  BrowserMultiFormatReaderMock.listVideoInputDevices = mockListVideoInputDevices;

  global.zxingMocks = {
    mockReset,
    mockDecodeFromVideoDevice,
    mockDecodeFromVideoElement,
    mockListVideoInputDevices,
  };

  return {
    BrowserMultiFormatReader: BrowserMultiFormatReaderMock,
    NotFoundException: class NotFoundException extends Error {
      constructor(message?: string) {
        super(message);
        this.name = 'NotFoundException';
      }
    },
    DecodeHintType: {
      TRY_HARDER: 3,
      POSSIBLE_FORMATS: 2,
      ASSUME_GS1: 11,
      RETURN_CODABAR_START_END: 12,
    },
    BarcodeFormat: {
      EAN_13: 'EAN_13',
      EAN_8: 'EAN_8',
      UPC_A: 'UPC_A',
      UPC_E: 'UPC_E',
      CODE_128: 'CODE_128',
      CODE_39: 'CODE_39',
      CODE_93: 'CODE_93',
      CODABAR: 'CODABAR',
      ITF: 'ITF',
      QR_CODE: 'QR_CODE',
      DATA_MATRIX: 'DATA_MATRIX',
      PDF_417: 'PDF_417',
    },
  };
});

// Mock navigator.mediaDevices
const mockGetUserMedia = vi.fn();
Object.defineProperty(navigator, 'mediaDevices', {
  value: {
    getUserMedia: mockGetUserMedia,
  },
  writable: true,
});

// Mock MediaStreamTrack
const mockTrack = {
  stop: vi.fn(),
  getSettings: vi.fn(() => ({
    width: 1280,
    height: 720,
    frameRate: 30,
    facingMode: 'environment',
    deviceId: 'camera1'
  })),
};

const mockStream = {
  getTracks: vi.fn(() => [mockTrack]),
  getVideoTracks: vi.fn(() => [mockTrack]),
};

describe('BarcodeScanner', () => {
  const mockOnClose = vi.fn();
  const mockOnScan = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockGetUserMedia.mockResolvedValue(mockStream);

    // Mock video element methods
    Object.defineProperty(HTMLVideoElement.prototype, 'play', {
      writable: true,
      value: vi.fn().mockResolvedValue(undefined),
    });

    Object.defineProperty(HTMLVideoElement.prototype, 'onloadedmetadata', {
      writable: true,
      value: null,
    });

    // Reset the decode mock to return a promise that never resolves by default
    global.zxingMocks.mockDecodeFromVideoElement.mockImplementation(() => new Promise(() => {}));
  });

  const renderBarcodeScanner = (open = true) => {
    return render(
      <BarcodeScanner
        open={open}
        onClose={mockOnClose}
        onScan={mockOnScan}
      />
    );
  };

  it('renders when open', () => {
    renderBarcodeScanner();
    expect(screen.getByText('Scan Barcode')).toBeInTheDocument();
    expect(screen.getByTestId('barcode-scanner-close')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    renderBarcodeScanner(false);
    expect(screen.queryByText('Scan Barcode')).not.toBeInTheDocument();
  });

  it('shows camera view when permission is granted initially', async () => {
    renderBarcodeScanner();

    await waitFor(() => {
      expect(screen.getByTestId('barcode-scanner-video')).toBeInTheDocument();
    });
  });

  it('shows camera view when permission is granted', async () => {
    renderBarcodeScanner();
    
    await waitFor(() => {
      expect(screen.getByTestId('barcodeScanner-video-main')).toBeInTheDocument();
    });

    expect(screen.getByText('Position the food product barcode within the camera view. Hold steady and ensure good lighting for best results.')).toBeInTheDocument();
  });

  it('shows error when camera permission is denied', async () => {
    const permissionError = new Error('Permission denied');
    permissionError.name = 'NotAllowedError';
    mockGetUserMedia.mockRejectedValue(permissionError);

    renderBarcodeScanner();
    
    await waitFor(() => {
      expect(screen.getByText('Camera permission denied. Please allow camera access to scan barcodes.')).toBeInTheDocument();
    });
  });

  it('shows error when no camera is found', async () => {
    const cameraError = new Error('No camera found');
    cameraError.name = 'NotFoundError';
    mockGetUserMedia.mockRejectedValue(cameraError);

    renderBarcodeScanner();
    
    await waitFor(() => {
      expect(screen.getByText('No camera found. Please ensure your device has a camera.')).toBeInTheDocument();
    });
  });

  it('shows generic error for other camera errors', async () => {
    const genericError = new Error('Generic camera error');
    mockGetUserMedia.mockRejectedValue(genericError);

    renderBarcodeScanner();
    
    await waitFor(() => {
      expect(screen.getByText('Failed to access camera: Generic camera error')).toBeInTheDocument();
    });
  });

  it('initializes camera scanning properly', async () => {
    renderBarcodeScanner();

    // Wait for the video element to be rendered
    await waitFor(() => {
      expect(screen.getByTestId('barcodeScanner-video-main')).toBeInTheDocument();
    });

    // Verify that the ZXing library methods are called
    expect(global.zxingMocks.mockListVideoInputDevices).toHaveBeenCalled();
    expect(mockGetUserMedia).toHaveBeenCalled();
  });

  it('handles scanning errors gracefully', async () => {
    // Create a NotFoundException-like error
    const notFoundError = new Error('No barcode found');
    notFoundError.name = 'NotFoundException';

    // Mock scanning error (NotFoundException should be ignored)
    global.zxingMocks.mockDecodeFromVideoElement.mockRejectedValue(notFoundError);

    renderBarcodeScanner();

    await waitFor(() => {
      expect(screen.getByTestId('barcodeScanner-video-main')).toBeInTheDocument();
    });

    // Wait a bit to ensure scanning attempts are made
    await new Promise(resolve => setTimeout(resolve, 200));

    // Should not call onScan or onClose for NotFoundException
    expect(mockOnScan).not.toHaveBeenCalled();
    expect(mockOnClose).not.toHaveBeenCalled();
  });

  it('closes scanner when close button is clicked', async () => {
    const user = userEvent.setup();
    renderBarcodeScanner();

    const closeButton = screen.getByTestId('barcode-scanner-close');
    await user.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('handles manual barcode input', async () => {
    const user = userEvent.setup();
    renderBarcodeScanner();

    // Find the manual input field and submit button
    const input = screen.getByTestId('barcodeScanner-input-manualBarcode').querySelector('input') as HTMLInputElement;
    const submitButton = screen.getByTestId('barcodeScanner-button-manualSubmit');

    // Check placeholder text for food products
    expect(input).toHaveAttribute('placeholder', 'e.g., 041196912586 (12-13 digits)');

    // Initially submit button should be disabled
    expect(submitButton).toBeDisabled();

    // Type a food product barcode
    await user.type(input, '041196912586');

    // Submit button should now be enabled
    expect(submitButton).not.toBeDisabled();

    // Click submit
    await user.click(submitButton);

    // Should call onScan with the manual barcode
    expect(mockOnScan).toHaveBeenCalledWith('041196912586');
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('stops scanning when component is closed', async () => {
    const { rerender } = renderBarcodeScanner(true);

    await waitFor(() => {
      expect(screen.getByTestId('barcodeScanner-video-main')).toBeInTheDocument();
    });

    // Close the scanner
    rerender(
      <BarcodeScanner
        open={false}
        onClose={mockOnClose}
        onScan={mockOnScan}
      />
    );

    // Wait for cleanup to complete - the video element should be removed
    await waitFor(() => {
      expect(screen.queryByTestId('barcodeScanner-video-main')).not.toBeInTheDocument();
    }, { timeout: 1000 });
  });

  it('handles no camera devices available', async () => {
    // Override the mock for this specific test
    global.zxingMocks.mockListVideoInputDevices.mockResolvedValueOnce([]);

    renderBarcodeScanner();

    await waitFor(() => {
      expect(screen.getByText('Failed to access camera: No camera devices found')).toBeInTheDocument();
    });
  });

  it('cleans up resources on unmount', async () => {
    const { unmount } = renderBarcodeScanner();

    await waitFor(() => {
      expect(screen.getByTestId('barcodeScanner-video-main')).toBeInTheDocument();
    });

    unmount();

    // Wait for cleanup to complete - component should be unmounted
    await waitFor(() => {
      expect(screen.queryByTestId('barcodeScanner-video-main')).not.toBeInTheDocument();
    }, { timeout: 1000 });
  });

  it('handles dialog close event', async () => {
    renderBarcodeScanner();

    // Get the dialog element
    const dialog = screen.getByTestId('barcode-scanner-dialog');

    // Simulate dialog close event (Escape key)
    fireEvent.keyDown(dialog, { key: 'Escape' });

    // Wait for the async close handler to complete
    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
    }, { timeout: 1000 });
  });

  it('shows scanning animation overlay', async () => {
    renderBarcodeScanner();

    await waitFor(() => {
      expect(screen.getByTestId('barcode-scanner-video')).toBeInTheDocument();
    });

    // Check that the scanning overlay is present (it's a styled Box)
    const videoContainer = screen.getByTestId('barcode-scanner-video').parentElement;
    expect(videoContainer).toHaveStyle('position: relative');
  });

  it('handles test barcode button', async () => {
    const user = userEvent.setup();
    renderBarcodeScanner();

    // Find and click the test barcode button
    const testButton = screen.getByTestId('test-barcode-button');
    expect(testButton).toBeInTheDocument();
    expect(testButton).toHaveTextContent('Use Test Food Product (041196912586)');

    await user.click(testButton);

    // Should call onScan with the food product test barcode
    expect(mockOnScan).toHaveBeenCalledWith('041196912586');
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('shows debug information during scanning', async () => {
    renderBarcodeScanner();

    await waitFor(() => {
      expect(screen.getByTestId('barcode-scanner-video')).toBeInTheDocument();
    });

    // Wait for some scanning attempts to occur
    await new Promise(resolve => setTimeout(resolve, 500));

    // Debug info should appear after some scanning attempts
    // Note: This test might be flaky due to timing, but it tests the concept
  });

  it('handles enhanced barcode format configuration', async () => {
    renderBarcodeScanner();

    await waitFor(() => {
      expect(screen.getByTestId('barcode-scanner-video')).toBeInTheDocument();
    });

    // Verify that the ZXing library was initialized with enhanced hints
    expect(global.zxingMocks.mockListVideoInputDevices).toHaveBeenCalled();
    expect(mockGetUserMedia).toHaveBeenCalled();
  });
});
